//
//  SmartReminderWindowView.swift
//  LifeTimer
//
//  Created by Developer on 2024.
//

import SwiftUI

/// 智能提醒窗口视图 - SwiftUI 原生窗口包装器
struct SmartReminderWindowView: View {
    @EnvironmentObject var timerModel: TimerModel
    @EnvironmentObject var smartReminderManager: SmartReminderManager
    @EnvironmentObject var swiftUIWindowManager: SwiftUIWindowManager

    @State private var selectedTask: String = ""
    
    var body: some View {
        Group {
            if swiftUIWindowManager.shouldShowSmartReminder {
                SmartReminderDialog(
                    isPresented: .constant(true),
                    timerModel: timerModel,
                    reminderManager: smartReminderManager,
                    selectedTask: selectedTask
                )
                .onAppear {
                    // 获取当前选中的任务
                    selectedTask = getCurrentTask()
                }
                .onChange(of: smartReminderManager.reminderState) { newState in
                    // 当提醒状态变为非显示状态时，关闭窗口
                    if newState != .showing {
                        swiftUIWindowManager.hideSmartReminderWindow()
                    }
                }
                .frame(width: 480, height: 600)
                .background(
                    // 毛玻璃背景
                    GlassEffectBackground()
                )
                .toolbar(.hidden) // 隐藏所有工具栏
            } else {
                // 当不应该显示时，显示一个空视图
                EmptyView()
            }
        }
    }
    
    /// 获取当前任务
    private func getCurrentTask() -> String {
        // 简单返回默认任务，实际任务会通过 SmartReminderManager 传递
        return "专注工作"
    }
}

#Preview {
    SmartReminderWindowView()
        .environmentObject(TimerModel())
        .environmentObject(SmartReminderManager())
}
